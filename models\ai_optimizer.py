"""
AI-Powered Train Traffic Optimization Engine
Hybrid RL-MILP system for train precedence and crossing decisions
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import pulp
import random
from collections import deque
import json
import time

from .railway_network import RailwayNetwork, TrainPriority


class TrainStatus(Enum):
    SCHEDULED = "scheduled"
    RUNNING = "running"
    DELAYED = "delayed"
    WAITING = "waiting"
    COMPLETED = "completed"


@dataclass
class Train:
    """Train entity with schedule and status"""
    id: str
    route: List[str]  # station IDs
    priority: TrainPriority
    scheduled_times: List[float]  # arrival times at each station
    current_position: int  # index in route
    current_time: float
    delay: float = 0.0
    status: TrainStatus = TrainStatus.SCHEDULED
    
    def get_next_station(self) -> Optional[str]:
        if self.current_position + 1 < len(self.route):
            return self.route[self.current_position + 1]
        return None
        
    def get_current_station(self) -> Optional[str]:
        if 0 <= self.current_position < len(self.route):
            return self.route[self.current_position]
        return None


class TrafficState:
    """Current state of railway traffic"""
    
    def __init__(self, network: RailwayNetwork):
        self.network = network
        self.trains: Dict[str, Train] = {}
        self.current_time = 0.0
        self.track_occupancy: Dict[str, Optional[str]] = {}  # track_id -> train_id
        self.station_occupancy: Dict[str, List[str]] = {}  # station_id -> [train_ids]
        
        # Initialize occupancy
        for track_id in network.tracks:
            self.track_occupancy[track_id] = None
        for station_id in network.stations:
            self.station_occupancy[station_id] = []
    
    def add_train(self, train: Train):
        """Add train to the system"""
        self.trains[train.id] = train
        
    def get_state_vector(self) -> np.ndarray:
        """Convert current state to vector for RL"""
        # State includes: train positions, delays, track occupancy, priorities
        state_size = len(self.network.stations) * 4 + len(self.network.tracks)
        state = np.zeros(state_size)
        
        idx = 0
        # Station occupancy and delays
        for station_id in sorted(self.network.stations.keys()):
            occupancy = len(self.station_occupancy[station_id])
            total_delay = sum(self.trains[tid].delay for tid in self.station_occupancy[station_id])
            avg_priority = np.mean([self.trains[tid].priority.value 
                                  for tid in self.station_occupancy[station_id]]) if occupancy > 0 else 0
            capacity_util = occupancy / self.network.stations[station_id].capacity
            
            state[idx:idx+4] = [occupancy, total_delay, avg_priority, capacity_util]
            idx += 4
            
        # Track occupancy
        for track_id in sorted(self.network.tracks.keys()):
            state[idx] = 1.0 if self.track_occupancy[track_id] is not None else 0.0
            idx += 1
            
        return state


class DQNAgent(nn.Module):
    """Deep Q-Network for train scheduling decisions"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 256):
        super(DQNAgent, self).__init__()
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size)
        self.fc4 = nn.Linear(hidden_size, action_size)
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.relu(self.fc3(x))
        x = self.fc4(x)
        return x


class MILPOptimizer:
    """Mixed Integer Linear Programming optimizer for constraint satisfaction"""
    
    def __init__(self, network: RailwayNetwork):
        self.network = network
        
    def optimize_schedule(self, trains: List[Train], time_horizon: float = 60.0) -> Dict[str, Any]:
        """Optimize train schedule using MILP"""
        
        # Create optimization problem
        prob = pulp.LpProblem("TrainScheduling", pulp.LpMinimize)
        
        # Decision variables
        # x[train_id][station_id][time_slot] = 1 if train is at station at time
        time_slots = int(time_horizon)
        x = {}
        
        for train in trains:
            x[train.id] = {}
            for station_id in self.network.stations:
                x[train.id][station_id] = {}
                for t in range(time_slots):
                    x[train.id][station_id][t] = pulp.LpVariable(
                        f"x_{train.id}_{station_id}_{t}", cat='Binary'
                    )
        
        # Objective: minimize total delay
        delay_vars = {}
        for train in trains:
            delay_vars[train.id] = pulp.LpVariable(f"delay_{train.id}", lowBound=0)
            
        prob += pulp.lpSum([
            delay_vars[train.id] * train.priority.value for train in trains
        ])
        
        # Constraints
        for train in trains:
            # Each train must be at exactly one station at each time
            for t in range(time_slots):
                prob += pulp.lpSum([
                    x[train.id][station_id][t] 
                    for station_id in self.network.stations
                ]) == 1
                
            # Route constraints - train must follow its route
            for i, station_id in enumerate(train.route):
                if i < len(train.scheduled_times):
                    scheduled_time = int(train.scheduled_times[i])
                    if scheduled_time < time_slots:
                        # Train should be at this station around scheduled time
                        prob += x[train.id][station_id][scheduled_time] >= 0.5
        
        # Capacity constraints
        for station_id, station in self.network.stations.items():
            for t in range(time_slots):
                prob += pulp.lpSum([
                    x[train.id][station_id][t] for train in trains
                ]) <= station.capacity
        
        # Track capacity constraints
        for track_id, track in self.network.tracks.items():
            for t in range(time_slots - 1):
                # Only one train can use track at a time for single track
                if track.track_type.value == "single":
                    prob += pulp.lpSum([
                        x[train.id][track.from_station][t] * x[train.id][track.to_station][t+1]
                        for train in trains
                    ]) <= 1
        
        # Solve
        prob.solve(pulp.PULP_CBC_CMD(msg=0))
        
        # Extract solution
        solution = {
            'status': pulp.LpStatus[prob.status],
            'objective': pulp.value(prob.objective),
            'schedule': {}
        }
        
        if prob.status == pulp.LpStatusOptimal:
            for train in trains:
                solution['schedule'][train.id] = {}
                for station_id in self.network.stations:
                    for t in range(time_slots):
                        if pulp.value(x[train.id][station_id][t]) > 0.5:
                            solution['schedule'][train.id][station_id] = t
        
        return solution


class HybridOptimizer:
    """Hybrid RL-MILP optimization system"""
    
    def __init__(self, network: RailwayNetwork, state_size: int = None, action_size: int = 10):
        self.network = network
        self.state_size = state_size or (len(network.stations) * 4 + len(network.tracks))
        self.action_size = action_size
        
        # RL Agent
        self.dqn = DQNAgent(self.state_size, self.action_size)
        self.target_dqn = DQNAgent(self.state_size, self.action_size)
        self.optimizer = optim.Adam(self.dqn.parameters(), lr=0.001)
        
        # MILP Optimizer
        self.milp_optimizer = MILPOptimizer(network)
        
        # Experience replay
        self.memory = deque(maxlen=10000)
        self.epsilon = 1.0
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01
        
        # Performance metrics
        self.metrics = {
            'total_delay': 0.0,
            'throughput': 0.0,
            'decisions_made': 0,
            'optimization_time': 0.0
        }
        
    def get_action(self, state: np.ndarray, use_epsilon: bool = True) -> int:
        """Get action from RL agent"""
        if use_epsilon and random.random() < self.epsilon:
            return random.randint(0, self.action_size - 1)
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        q_values = self.dqn(state_tensor)
        return q_values.argmax().item()
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.append((state, action, reward, next_state, done))
    
    def replay(self, batch_size: int = 32):
        """Train the RL agent"""
        if len(self.memory) < batch_size:
            return
            
        batch = random.sample(self.memory, batch_size)
        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])
        
        current_q_values = self.dqn(states).gather(1, actions.unsqueeze(1))
        next_q_values = self.target_dqn(next_states).max(1)[0].detach()
        target_q_values = rewards + (0.99 * next_q_values * ~dones)
        
        loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
    
    def optimize_traffic(self, traffic_state: TrafficState) -> Dict[str, Any]:
        """Main optimization function combining RL and MILP"""
        start_time = time.time()
        
        # Get current state
        state = traffic_state.get_state_vector()
        
        # RL decision for high-level strategy
        rl_action = self.get_action(state)
        
        # Convert RL action to optimization parameters
        time_horizon = 30.0 + (rl_action % 3) * 15.0  # 30, 45, or 60 minutes
        priority_weight = 1.0 + (rl_action // 3) * 0.5  # Priority weighting
        
        # MILP optimization for detailed scheduling
        trains = list(traffic_state.trains.values())
        milp_solution = self.milp_optimizer.optimize_schedule(trains, time_horizon)
        
        # Calculate reward for RL
        reward = self.calculate_reward(traffic_state, milp_solution)
        
        # Store experience for training
        # (This would be done in a full training loop)
        
        optimization_time = time.time() - start_time
        self.metrics['optimization_time'] = optimization_time
        self.metrics['decisions_made'] += 1
        
        return {
            'rl_action': rl_action,
            'milp_solution': milp_solution,
            'optimization_time': optimization_time,
            'recommendations': self.generate_recommendations(milp_solution),
            'metrics': self.metrics.copy()
        }
    
    def calculate_reward(self, traffic_state: TrafficState, solution: Dict) -> float:
        """Calculate reward for RL training"""
        if solution['status'] != 'Optimal':
            return -100.0
        
        # Reward based on minimized delay and maximized throughput
        total_delay = sum(train.delay for train in traffic_state.trains.values())
        throughput = len([t for t in traffic_state.trains.values() 
                         if t.status == TrainStatus.COMPLETED])
        
        reward = -total_delay * 0.1 + throughput * 10.0
        return reward
    
    def generate_recommendations(self, solution: Dict) -> List[Dict]:
        """Generate human-readable recommendations"""
        recommendations = []
        
        if solution['status'] == 'Optimal':
            recommendations.append({
                'type': 'optimization_success',
                'message': f"Optimal schedule found with objective value: {solution['objective']:.2f}",
                'priority': 'info'
            })
            
            # Add specific train recommendations
            for train_id, schedule in solution.get('schedule', {}).items():
                recommendations.append({
                    'type': 'train_schedule',
                    'train_id': train_id,
                    'message': f"Train {train_id} schedule optimized",
                    'schedule': schedule,
                    'priority': 'normal'
                })
        else:
            recommendations.append({
                'type': 'optimization_failed',
                'message': "Could not find optimal solution. Manual intervention may be required.",
                'priority': 'high'
            })
        
        return recommendations
    
    def update_target_network(self):
        """Update target network for stable training"""
        self.target_dqn.load_state_dict(self.dqn.state_dict())
    
    def save_model(self, filepath: str):
        """Save trained model"""
        torch.save({
            'dqn_state_dict': self.dqn.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'metrics': self.metrics
        }, filepath)
    
    def load_model(self, filepath: str):
        """Load trained model"""
        checkpoint = torch.load(filepath)
        self.dqn.load_state_dict(checkpoint['dqn_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.metrics = checkpoint['metrics']
        self.update_target_network()


if __name__ == "__main__":
    # Test the optimizer
    from railway_network import create_sample_network
    
    network = create_sample_network()
    optimizer = HybridOptimizer(network)
    
    # Create sample traffic state
    traffic_state = TrafficState(network)
    
    # Add sample train
    train = Train(
        id="12951",
        route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"],
        priority=TrainPriority.EXPRESS,
        scheduled_times=[0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5],
        current_position=0,
        current_time=0.0
    )
    traffic_state.add_train(train)
    
    # Test optimization
    result = optimizer.optimize_traffic(traffic_state)
    print("Optimization result:", result)
