"""
Railway Network Graph Model
Represents the railway infrastructure as a graph with stations, tracks, signals, and constraints.
"""

import networkx as nx
import numpy as np
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class TrackType(Enum):
    SINGLE = "single"
    DOUBLE = "double"
    MULTIPLE = "multiple"


class SignalType(Enum):
    AUTOMATIC = "automatic"
    SEMI_AUTOMATIC = "semi_automatic"
    MANUAL = "manual"


class TrainPriority(Enum):
    EXPRESS = 1
    PASSENGER = 2
    FREIGHT = 3
    MAINTENANCE = 4


@dataclass
class Station:
    """Railway station node"""
    id: str
    name: str
    latitude: float
    longitude: float
    platforms: int
    capacity: int
    is_junction: bool = False
    elevation: float = 0.0
    
    def to_dict(self):
        return asdict(self)


@dataclass
class Track:
    """Railway track edge"""
    id: str
    from_station: str
    to_station: str
    distance: float  # in kilometers
    track_type: TrackType
    max_speed: float  # km/h
    gradient: float  # percentage
    capacity: int  # trains per hour
    electrified: bool = True
    
    def to_dict(self):
        return {
            **asdict(self),
            'track_type': self.track_type.value
        }


@dataclass
class Signal:
    """Railway signal"""
    id: str
    station_id: str
    signal_type: SignalType
    position: Tuple[float, float]  # lat, lon
    
    def to_dict(self):
        return {
            **asdict(self),
            'signal_type': self.signal_type.value
        }


class RailwayNetwork:
    """Graph-based railway network representation"""
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.stations: Dict[str, Station] = {}
        self.tracks: Dict[str, Track] = {}
        self.signals: Dict[str, Signal] = {}
        
    def add_station(self, station: Station):
        """Add a station to the network"""
        self.stations[station.id] = station
        self.graph.add_node(
            station.id,
            **station.to_dict(),
            node_type='station'
        )
        
    def add_track(self, track: Track):
        """Add a track between stations"""
        self.tracks[track.id] = track
        
        # Add bidirectional edges for double/multiple tracks
        if track.track_type in [TrackType.DOUBLE, TrackType.MULTIPLE]:
            self.graph.add_edge(
                track.from_station,
                track.to_station,
                **track.to_dict(),
                edge_type='track'
            )
            # Reverse direction
            reverse_track = Track(
                id=f"{track.id}_reverse",
                from_station=track.to_station,
                to_station=track.from_station,
                distance=track.distance,
                track_type=track.track_type,
                max_speed=track.max_speed,
                gradient=-track.gradient,
                capacity=track.capacity,
                electrified=track.electrified
            )
            self.graph.add_edge(
                track.to_station,
                track.from_station,
                **reverse_track.to_dict(),
                edge_type='track'
            )
        else:
            # Single track - add single direction
            self.graph.add_edge(
                track.from_station,
                track.to_station,
                **track.to_dict(),
                edge_type='track'
            )
            
    def add_signal(self, signal: Signal):
        """Add a signal to the network"""
        self.signals[signal.id] = signal
        
    def get_shortest_path(self, from_station: str, to_station: str, 
                         weight: str = 'distance') -> List[str]:
        """Find shortest path between stations"""
        try:
            return nx.shortest_path(self.graph, from_station, to_station, weight=weight)
        except nx.NetworkXNoPath:
            return []
            
    def get_alternative_routes(self, from_station: str, to_station: str, 
                             k: int = 3) -> List[List[str]]:
        """Get k alternative routes between stations"""
        try:
            paths = list(nx.shortest_simple_paths(
                self.graph, from_station, to_station, weight='distance'
            ))
            return paths[:k]
        except nx.NetworkXNoPath:
            return []
            
    def calculate_route_metrics(self, route: List[str]) -> Dict:
        """Calculate metrics for a given route"""
        if len(route) < 2:
            return {}
            
        total_distance = 0
        total_time = 0
        min_capacity = float('inf')
        max_gradient = 0
        
        for i in range(len(route) - 1):
            edge_data = self.graph[route[i]][route[i + 1]]
            total_distance += edge_data['distance']
            total_time += edge_data['distance'] / edge_data['max_speed']
            min_capacity = min(min_capacity, edge_data['capacity'])
            max_gradient = max(max_gradient, abs(edge_data['gradient']))
            
        return {
            'distance': total_distance,
            'estimated_time': total_time,
            'bottleneck_capacity': min_capacity,
            'max_gradient': max_gradient,
            'stations': len(route)
        }
        
    def get_station_neighbors(self, station_id: str) -> List[str]:
        """Get neighboring stations"""
        return list(self.graph.neighbors(station_id))
        
    def get_network_stats(self) -> Dict:
        """Get network statistics"""
        return {
            'stations': len(self.stations),
            'tracks': len(self.tracks),
            'signals': len(self.signals),
            'connectivity': nx.is_connected(self.graph.to_undirected()),
            'average_degree': sum(dict(self.graph.degree()).values()) / len(self.graph),
            'diameter': nx.diameter(self.graph.to_undirected()) if nx.is_connected(self.graph.to_undirected()) else -1
        }
        
    def export_to_json(self, filepath: str):
        """Export network to JSON format"""
        data = {
            'stations': [station.to_dict() for station in self.stations.values()],
            'tracks': [track.to_dict() for track in self.tracks.values()],
            'signals': [signal.to_dict() for signal in self.signals.values()]
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
            
    def load_from_json(self, filepath: str):
        """Load network from JSON format"""
        with open(filepath, 'r') as f:
            data = json.load(f)
            
        # Load stations
        for station_data in data['stations']:
            station = Station(**station_data)
            self.add_station(station)
            
        # Load tracks
        for track_data in data['tracks']:
            track_data['track_type'] = TrackType(track_data['track_type'])
            track = Track(**track_data)
            self.add_track(track)
            
        # Load signals
        for signal_data in data['signals']:
            signal_data['signal_type'] = SignalType(signal_data['signal_type'])
            signal = Signal(**signal_data)
            self.add_signal(signal)


def create_sample_network() -> RailwayNetwork:
    """Create a sample railway network for testing"""
    network = RailwayNetwork()
    
    # Add stations (Delhi-Mumbai corridor sample)
    stations = [
        Station("DEL", "New Delhi", 28.6139, 77.2090, 16, 50, True),
        Station("GZB", "Ghaziabad", 28.6692, 77.4538, 8, 30),
        Station("ALD", "Allahabad", 25.4358, 81.8463, 10, 35, True),
        Station("JHS", "Jhansi", 25.4484, 78.5685, 6, 25, True),
        Station("BPL", "Bhopal", 23.2599, 77.4126, 8, 30, True),
        Station("NGP", "Nagpur", 21.1458, 79.0882, 10, 35, True),
        Station("BSL", "Bhusaval", 21.0444, 75.7849, 6, 25, True),
        Station("MMR", "Manmad", 20.2551, 74.4399, 4, 20),
        Station("NK", "Nashik", 19.9975, 73.7898, 4, 20),
        Station("KYN", "Kalyan", 19.2437, 73.1355, 12, 40, True),
        Station("CSTM", "Mumbai CST", 18.9398, 72.8355, 18, 60, True)
    ]
    
    for station in stations:
        network.add_station(station)
    
    # Add tracks
    tracks = [
        Track("DEL-GZB", "DEL", "GZB", 25, TrackType.DOUBLE, 110, 0.5, 24),
        Track("GZB-ALD", "GZB", "ALD", 635, TrackType.DOUBLE, 130, 1.2, 20),
        Track("ALD-JHS", "ALD", "JHS", 228, TrackType.DOUBLE, 110, 0.8, 18),
        Track("JHS-BPL", "JHS", "BPL", 302, TrackType.DOUBLE, 110, 1.5, 16),
        Track("BPL-NGP", "BPL", "NGP", 590, TrackType.DOUBLE, 110, 0.9, 18),
        Track("NGP-BSL", "NGP", "BSL", 283, TrackType.DOUBLE, 100, 1.1, 16),
        Track("BSL-MMR", "BSL", "MMR", 181, TrackType.DOUBLE, 100, 0.7, 16),
        Track("MMR-NK", "MMR", "NK", 35, TrackType.DOUBLE, 80, 2.1, 12),
        Track("NK-KYN", "NK", "KYN", 165, TrackType.DOUBLE, 100, 1.8, 18),
        Track("KYN-CSTM", "KYN", "CSTM", 54, TrackType.MULTIPLE, 80, 0.3, 30)
    ]
    
    for track in tracks:
        network.add_track(track)
    
    return network


if __name__ == "__main__":
    # Test the network
    network = create_sample_network()
    print("Network Stats:", network.get_network_stats())
    
    # Test routing
    route = network.get_shortest_path("DEL", "CSTM")
    print("Shortest route DEL->CSTM:", route)
    
    if route:
        metrics = network.calculate_route_metrics(route)
        print("Route metrics:", metrics)
