/**
 * AI-Powered Train Traffic Control System
 * Frontend JavaScript Application
 */

class TrafficControlApp {
    constructor() {
        this.socket = null;
        this.map = null;
        this.chart = null;
        this.trains = {};
        this.networkData = null;
        this.isConnected = false;
        this.simulationRunning = false;
        this.autoOptimize = true;
        
        this.init();
    }

    init() {
        this.initializeSocket();
        this.initializeMap();
        this.initializeChart();
        this.setupEventListeners();
        this.fetchInitialData();
    }

    initializeSocket() {
        this.socket = io('/simulation');
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.isConnected = true;
            this.updateSystemStatus('online');
            this.showNotification('Connected to AI Traffic Control System', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.isConnected = false;
            this.updateSystemStatus('offline');
            this.showNotification('Disconnected from server', 'error');
        });

        this.socket.on('simulation_update', (data) => {
            this.handleSimulationUpdate(data);
        });

        this.socket.on('train_event', (event) => {
            this.handleTrainEvent(event);
        });

        this.socket.on('optimization_result', (result) => {
            this.handleOptimizationResult(result);
        });

        this.socket.on('status_update', (status) => {
            this.updateSimulationStatus(status);
        });
    }

    initializeMap() {
        this.map = L.map('map').setView([23.0, 77.0], 6);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);

        // Custom train icon
        this.trainIcon = L.divIcon({
            className: 'train-marker',
            html: '🚂',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });

        // Station icon
        this.stationIcon = L.divIcon({
            className: 'station-marker',
            html: '🏢',
            iconSize: [16, 16],
            iconAnchor: [8, 8]
        });
    }

    initializeChart() {
        const ctx = document.getElementById('metricsChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Throughput',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Avg Delay',
                        data: [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    setupEventListeners() {
        // Simulation controls
        document.getElementById('startBtn').addEventListener('click', () => this.startSimulation());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseSimulation());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopSimulation());
        document.getElementById('stepBtn').addEventListener('click', () => this.stepSimulation());
        
        // AI controls
        document.getElementById('optimizeBtn').addEventListener('click', () => this.runOptimization());
        document.getElementById('autoOptimizeBtn').addEventListener('click', () => this.toggleAutoOptimize());
        
        // Speed control
        const speedSlider = document.getElementById('speedSlider');
        speedSlider.addEventListener('input', (e) => {
            const speed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = speed.toFixed(1);
            this.updateSimulationSpeed(speed);
        });
    }

    async fetchInitialData() {
        try {
            // Fetch network data
            const networkResponse = await fetch('/api/network/info');
            this.networkData = await networkResponse.json();
            this.renderNetwork();

            // Fetch trains
            const trainsResponse = await fetch('/api/trains');
            this.trains = await trainsResponse.json();
            this.renderTrains();

            // Fetch metrics
            const metricsResponse = await fetch('/api/metrics');
            const metricsData = await metricsResponse.json();
            this.updateMetrics(metricsData.current);

        } catch (error) {
            console.error('Error fetching initial data:', error);
            this.showNotification('Failed to load initial data', 'error');
        }
    }

    renderNetwork() {
        if (!this.networkData || !this.networkData.stations) return;

        // Clear existing markers
        this.map.eachLayer((layer) => {
            if (layer instanceof L.Marker) {
                this.map.removeLayer(layer);
            }
        });

        // Add station markers
        this.networkData.stations.forEach(station => {
            const marker = L.marker([station.latitude, station.longitude], {
                icon: this.stationIcon
            }).addTo(this.map);
            
            marker.bindPopup(`
                <strong>${station.name}</strong><br>
                Platforms: ${station.platforms}<br>
                Capacity: ${station.capacity}<br>
                ${station.is_junction ? 'Junction Station' : ''}
            `);
        });

        // Add track lines
        if (this.networkData.tracks) {
            this.networkData.tracks.forEach(track => {
                const fromStation = this.networkData.stations.find(s => s.id === track.from_station);
                const toStation = this.networkData.stations.find(s => s.id === track.to_station);
                
                if (fromStation && toStation) {
                    const line = L.polyline([
                        [fromStation.latitude, fromStation.longitude],
                        [toStation.latitude, toStation.longitude]
                    ], {
                        color: track.track_type === 'double' ? '#2ecc71' : '#3498db',
                        weight: track.track_type === 'double' ? 4 : 2,
                        opacity: 0.7
                    }).addTo(this.map);
                    
                    line.bindPopup(`
                        <strong>${track.from_station} → ${track.to_station}</strong><br>
                        Distance: ${track.distance}km<br>
                        Max Speed: ${track.max_speed}km/h<br>
                        Type: ${track.track_type}<br>
                        Capacity: ${track.capacity} trains/hour
                    `);
                }
            });
        }
    }

    renderTrains() {
        const trainsList = document.getElementById('trainsList');
        
        if (Object.keys(this.trains).length === 0) {
            trainsList.innerHTML = '<div class="loading"><p>No active trains</p></div>';
            return;
        }

        trainsList.innerHTML = '';
        
        Object.values(this.trains).forEach(train => {
            const trainElement = document.createElement('div');
            trainElement.className = 'train-item';
            trainElement.innerHTML = `
                <div class="train-info">
                    <h4>Train ${train.id}</h4>
                    <p>Route: ${train.route.join(' → ')}</p>
                    <p>Delay: ${train.delay.toFixed(1)} min</p>
                </div>
                <div class="train-status status-${train.status}">
                    ${train.status.toUpperCase()}
                </div>
            `;
            trainsList.appendChild(trainElement);
        });
    }

    updateMetrics(metrics) {
        if (!metrics) return;

        document.getElementById('throughputValue').textContent = metrics.throughput || 0;
        document.getElementById('delayValue').textContent = (metrics.average_delay || 0).toFixed(1);
        document.getElementById('capacityValue').textContent = ((metrics.capacity_utilization || 0) * 100).toFixed(0) + '%';
        document.getElementById('activeTrainsValue').textContent = metrics.active_trains || 0;
    }

    updateChart(metricsHistory) {
        if (!metricsHistory || metricsHistory.length === 0) return;

        const labels = metricsHistory.map(m => new Date(m.time * 60000).toLocaleTimeString());
        const throughputData = metricsHistory.map(m => m.throughput || 0);
        const delayData = metricsHistory.map(m => m.average_delay || 0);

        this.chart.data.labels = labels.slice(-20); // Last 20 data points
        this.chart.data.datasets[0].data = throughputData.slice(-20);
        this.chart.data.datasets[1].data = delayData.slice(-20);
        this.chart.update();
    }

    handleSimulationUpdate(data) {
        if (data.trains) {
            this.trains = data.trains;
            this.renderTrains();
        }
        
        if (data.metrics) {
            this.updateMetrics(data.metrics);
        }
        
        if (data.time !== undefined) {
            this.updateCurrentTime(data.time);
        }
    }

    handleTrainEvent(event) {
        console.log('Train event:', event);
        this.showNotification(`Train ${event.data.train_id}: ${event.type.replace('_', ' ')}`, 'info');
        
        // Update train data if needed
        if (event.data.train_id && this.trains[event.data.train_id]) {
            // Update specific train data based on event
            this.renderTrains();
        }
    }

    handleOptimizationResult(result) {
        console.log('Optimization result:', result);
        this.showNotification('AI optimization completed successfully', 'success');
    }

    updateSystemStatus(status) {
        const statusElement = document.getElementById('systemStatus');
        statusElement.className = `status-indicator status-${status}`;
        statusElement.innerHTML = `
            <div class="status-dot"></div>
            <span>System ${status === 'online' ? 'Online' : 'Offline'}</span>
        `;
    }

    updateSimulationStatus(status) {
        this.simulationRunning = status.simulation_running;
        const simulationStatus = document.getElementById('simulationStatus');
        
        if (this.simulationRunning) {
            simulationStatus.style.display = 'block';
            simulationStatus.innerHTML = `<span>Simulation: ${status.simulation_paused ? 'Paused' : 'Running'}</span>`;
        } else {
            simulationStatus.style.display = 'none';
        }
        
        // Update button states
        document.getElementById('startBtn').disabled = this.simulationRunning;
        document.getElementById('pauseBtn').disabled = !this.simulationRunning;
        document.getElementById('stopBtn').disabled = !this.simulationRunning;
        
        if (status.current_time !== undefined) {
            this.updateCurrentTime(status.current_time);
        }
    }

    updateCurrentTime(time) {
        const hours = Math.floor(time / 60);
        const minutes = Math.floor(time % 60);
        document.getElementById('currentTime').textContent = 
            `Time: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    async startSimulation() {
        try {
            const response = await fetch('/api/simulation/start', { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                this.showNotification('Simulation started', 'success');
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Failed to start simulation', 'error');
        }
    }

    async pauseSimulation() {
        try {
            const response = await fetch('/api/simulation/pause', { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                this.showNotification(`Simulation ${result.status}`, 'info');
            }
        } catch (error) {
            this.showNotification('Failed to pause simulation', 'error');
        }
    }

    async stopSimulation() {
        try {
            const response = await fetch('/api/simulation/stop', { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                this.showNotification('Simulation stopped', 'info');
            }
        } catch (error) {
            this.showNotification('Failed to stop simulation', 'error');
        }
    }

    async stepSimulation() {
        try {
            const response = await fetch('/api/simulation/step', { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                this.showNotification('Simulation stepped forward', 'info');
            }
        } catch (error) {
            this.showNotification('Failed to step simulation', 'error');
        }
    }

    async runOptimization() {
        try {
            const response = await fetch('/api/optimization/run', { method: 'POST' });
            const result = await response.json();
            if (result.success) {
                this.showNotification('AI optimization triggered', 'success');
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Failed to run optimization', 'error');
        }
    }

    toggleAutoOptimize() {
        this.autoOptimize = !this.autoOptimize;
        const button = document.getElementById('autoOptimizeBtn');
        button.textContent = `🔄 Auto: ${this.autoOptimize ? 'ON' : 'OFF'}`;
        button.className = `btn ${this.autoOptimize ? 'btn-primary' : 'btn-secondary'}`;
        
        // Update server configuration
        this.updateSimulationConfig({ auto_optimize: this.autoOptimize });
    }

    async updateSimulationSpeed(speed) {
        await this.updateSimulationConfig({ speed_multiplier: speed });
    }

    async updateSimulationConfig(config) {
        try {
            const response = await fetch('/api/simulation/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });
            const result = await response.json();
            if (!result.success) {
                this.showNotification('Failed to update configuration', 'error');
            }
        } catch (error) {
            console.error('Error updating config:', error);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <strong>${type.toUpperCase()}</strong><br>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TrafficControlApp();
});

// Add slideOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .train-marker {
        font-size: 16px;
        text-align: center;
        line-height: 20px;
    }
    
    .station-marker {
        font-size: 12px;
        text-align: center;
        line-height: 16px;
    }
`;
document.head.appendChild(style);
