import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import io from 'socket.io-client';
import './App.css';

// Components
import Dashboard from './components/Dashboard';
import NetworkMap from './components/NetworkMap';
import TrainList from './components/TrainList';
import MetricsPanel from './components/MetricsPanel';
import ControlPanel from './components/ControlPanel';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import NotificationPanel from './components/NotificationPanel';

// Context
import { AppProvider } from './context/AppContext';

function App() {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [systemStatus, setSystemStatus] = useState('offline');
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Initialize WebSocket connection
    const newSocket = io('http://localhost:5000/simulation', {
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
      setSystemStatus('online');
      addNotification('Connected to AI Traffic Control System', 'success');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
      setSystemStatus('offline');
      addNotification('Disconnected from server', 'error');
    });

    newSocket.on('connected', (data) => {
      console.log('Server connection confirmed:', data);
      setSystemStatus(data.system_status);
    });

    newSocket.on('simulation_update', (data) => {
      // Handle simulation updates
      console.log('Simulation update:', data);
    });

    newSocket.on('train_event', (event) => {
      // Handle train events
      console.log('Train event:', event);
      addNotification(`Train ${event.data.train_id}: ${event.type}`, 'info');
    });

    newSocket.on('optimization_result', (result) => {
      // Handle optimization results
      console.log('Optimization result:', result);
      addNotification('AI optimization completed', 'success');
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  const addNotification = (message, type = 'info') => {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toISOString()
    };
    
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 5000);
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <AppProvider value={{ 
      socket, 
      connected, 
      systemStatus, 
      addNotification,
      notifications,
      removeNotification
    }}>
      <Router>
        <div className="app">
          <Header />
          <div className="app-body">
            <Sidebar />
            <main className="main-content">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/network" element={<NetworkMap />} />
                <Route path="/trains" element={<TrainList />} />
                <Route path="/metrics" element={<MetricsPanel />} />
                <Route path="/control" element={<ControlPanel />} />
              </Routes>
            </main>
          </div>
          <NotificationPanel 
            notifications={notifications}
            onRemove={removeNotification}
          />
        </div>
      </Router>
    </AppProvider>
  );
}

export default App;
