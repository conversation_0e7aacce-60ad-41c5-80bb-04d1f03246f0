<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Train Traffic Control System</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3498db, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
            margin-bottom: 15px;
        }

        .status-bar {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .status-online {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .status-offline {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-online .status-dot {
            background: #27ae60;
        }

        .status-offline .status-dot {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .dashboard {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .panel h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            font-weight: 600;
        }

        .map-panel {
            grid-row: 1 / 3;
            position: relative;
        }

        #map {
            width: 100%;
            height: calc(100% - 60px);
            border-radius: 10px;
            border: 2px solid rgba(52, 152, 219, 0.2);
        }

        .controls-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .control-group {
            background: rgba(236, 240, 241, 0.5);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(189, 195, 199, 0.3);
        }

        .control-group h3 {
            color: #34495e;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: rgba(236, 240, 241, 0.5);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(189, 195, 199, 0.3);
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
            font-weight: 500;
        }

        .chart-container {
            height: 200px;
            margin-top: 15px;
        }

        .train-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .train-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: rgba(236, 240, 241, 0.5);
            border-radius: 8px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .train-item:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .train-info h4 {
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .train-info p {
            color: #7f8c8d;
            font-size: 0.85em;
        }

        .train-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-running {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .status-delayed {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .status-scheduled {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(52, 152, 219, 0.3);
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-left: 4px solid #3498db;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            border-left-color: #27ae60;
        }

        .notification.error {
            border-left-color: #e74c3c;
        }

        .notification.warning {
            border-left-color: #f39c12;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚄 AI-Powered Train Traffic Control</h1>
            <p class="subtitle">Intelligent Railway Section Management with Real-time Optimization</p>
            <div class="status-bar">
                <div id="systemStatus" class="status-indicator status-offline">
                    <div class="status-dot"></div>
                    <span>System Offline</span>
                </div>
                <div id="simulationStatus" class="status-indicator" style="display: none;">
                    <span>Simulation: Stopped</span>
                </div>
                <div id="currentTime" style="color: #7f8c8d; font-weight: 500;">
                    Time: 00:00
                </div>
            </div>
        </div>

        <div class="dashboard">
            <!-- Map Panel -->
            <div class="panel map-panel">
                <h2>🗺️ Railway Network Overview</h2>
                <div id="map"></div>
            </div>

            <!-- Controls Panel -->
            <div class="panel controls-panel">
                <h2>🎮 Simulation Control</h2>
                
                <div class="control-group">
                    <h3>Simulation</h3>
                    <div class="button-group">
                        <button id="startBtn" class="btn btn-success">▶️ Start</button>
                        <button id="pauseBtn" class="btn btn-warning" disabled>⏸️ Pause</button>
                        <button id="stopBtn" class="btn btn-danger" disabled>⏹️ Stop</button>
                        <button id="stepBtn" class="btn btn-primary">⏭️ Step</button>
                    </div>
                </div>

                <div class="control-group">
                    <h3>AI Optimization</h3>
                    <div class="button-group">
                        <button id="optimizeBtn" class="btn btn-primary">🤖 Optimize Now</button>
                        <button id="autoOptimizeBtn" class="btn btn-primary">🔄 Auto: ON</button>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Speed Control</h3>
                    <input type="range" id="speedSlider" min="0.1" max="10" value="1" step="0.1" style="width: 100%; margin: 10px 0;">
                    <div style="text-align: center; color: #7f8c8d;">Speed: <span id="speedValue">1.0</span>x</div>
                </div>
            </div>

            <!-- Metrics Panel -->
            <div class="panel">
                <h2>📊 Performance Metrics</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div id="throughputValue" class="metric-value">0</div>
                        <div class="metric-label">Trains Completed</div>
                    </div>
                    <div class="metric-card">
                        <div id="delayValue" class="metric-value">0.0</div>
                        <div class="metric-label">Avg Delay (min)</div>
                    </div>
                    <div class="metric-card">
                        <div id="capacityValue" class="metric-value">0%</div>
                        <div class="metric-label">Capacity Usage</div>
                    </div>
                    <div class="metric-card">
                        <div id="activeTrainsValue" class="metric-value">0</div>
                        <div class="metric-label">Active Trains</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="metricsChart"></canvas>
                </div>
            </div>

            <!-- Trains Panel -->
            <div class="panel">
                <h2>🚂 Active Trains</h2>
                <div id="trainsList" class="train-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading trains...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Main Application Script -->
    <script src="/static/js/app.js"></script>
</body>
</html>
