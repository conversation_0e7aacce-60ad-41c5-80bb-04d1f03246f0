import React, { useState, useEffect, useContext } from 'react';
import { AppContext } from '../context/AppContext';
import NetworkMap from './NetworkMap';
import MetricsPanel from './MetricsPanel';
import TrainList from './TrainList';
import ControlPanel from './ControlPanel';
import './Dashboard.css';

const Dashboard = () => {
  const { socket, connected, systemStatus, addNotification } = useContext(AppContext);
  const [simulationData, setSimulationData] = useState({
    trains: {},
    metrics: {},
    time: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (socket && connected) {
      // Subscribe to simulation updates
      socket.emit('subscribe', { event_type: 'simulation_update' });
      socket.emit('subscribe', { event_type: 'train_event' });
      socket.emit('subscribe', { event_type: 'optimization_result' });

      // Request current status
      socket.emit('get_status');

      // Handle simulation updates
      socket.on('simulation_update', (data) => {
        setSimulationData(data);
        setIsLoading(false);
      });

      socket.on('status_update', (status) => {
        setSimulationData(prev => ({
          ...prev,
          ...status
        }));
        setIsLoading(false);
      });

      // Fetch initial data
      fetchInitialData();
    }

    return () => {
      if (socket) {
        socket.off('simulation_update');
        socket.off('status_update');
      }
    };
  }, [socket, connected]);

  const fetchInitialData = async () => {
    try {
      // Fetch network info
      const networkResponse = await fetch('/api/network/info');
      const networkData = await networkResponse.json();

      // Fetch trains
      const trainsResponse = await fetch('/api/trains');
      const trainsData = await trainsResponse.json();

      // Fetch metrics
      const metricsResponse = await fetch('/api/metrics');
      const metricsData = await metricsResponse.json();

      setSimulationData(prev => ({
        ...prev,
        network: networkData,
        trains: trainsData,
        metrics: metricsData
      }));

    } catch (error) {
      console.error('Error fetching initial data:', error);
      addNotification('Failed to load initial data', 'error');
    }
  };

  if (isLoading) {
    return (
      <div className="dashboard loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading AI Traffic Control System...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>AI-Powered Train Traffic Control</h1>
        <div className="system-status">
          <span className={`status-indicator ${systemStatus}`}></span>
          <span className="status-text">
            System {systemStatus === 'online' ? 'Online' : 'Offline'}
          </span>
          {simulationData.simulation_running && (
            <span className="simulation-status">
              Simulation Running - Time: {simulationData.current_time?.toFixed(1)}min
            </span>
          )}
        </div>
      </div>

      <div className="dashboard-grid">
        {/* Main Map View */}
        <div className="dashboard-section map-section">
          <div className="section-header">
            <h2>Network Overview</h2>
            <div className="section-controls">
              <button className="btn btn-secondary">
                <i className="icon-fullscreen"></i>
                Fullscreen
              </button>
            </div>
          </div>
          <NetworkMap 
            networkData={simulationData.network}
            trains={simulationData.trains}
            showTrains={true}
            showCapacity={true}
          />
        </div>

        {/* Control Panel */}
        <div className="dashboard-section control-section">
          <div className="section-header">
            <h2>Simulation Control</h2>
          </div>
          <ControlPanel 
            simulationRunning={simulationData.simulation_running}
            simulationPaused={simulationData.simulation_paused}
            currentTime={simulationData.current_time}
          />
        </div>

        {/* Metrics Panel */}
        <div className="dashboard-section metrics-section">
          <div className="section-header">
            <h2>Performance Metrics</h2>
            <div className="section-controls">
              <button className="btn btn-secondary">
                <i className="icon-download"></i>
                Export
              </button>
            </div>
          </div>
          <MetricsPanel metrics={simulationData.metrics} />
        </div>

        {/* Train List */}
        <div className="dashboard-section trains-section">
          <div className="section-header">
            <h2>Active Trains</h2>
            <div className="section-controls">
              <button className="btn btn-primary">
                <i className="icon-plus"></i>
                Add Train
              </button>
            </div>
          </div>
          <TrainList trains={simulationData.trains} />
        </div>

        {/* AI Recommendations */}
        <div className="dashboard-section recommendations-section">
          <div className="section-header">
            <h2>AI Recommendations</h2>
            <div className="section-controls">
              <button className="btn btn-primary">
                <i className="icon-refresh"></i>
                Optimize Now
              </button>
            </div>
          </div>
          <div className="recommendations-content">
            <div className="recommendation-item priority-high">
              <div className="recommendation-icon">
                <i className="icon-warning"></i>
              </div>
              <div className="recommendation-text">
                <h4>Track Congestion Detected</h4>
                <p>Consider rerouting Train 12952 via alternate route to reduce delays.</p>
                <span className="recommendation-time">2 minutes ago</span>
              </div>
              <div className="recommendation-actions">
                <button className="btn btn-sm btn-primary">Apply</button>
                <button className="btn btn-sm btn-secondary">Dismiss</button>
              </div>
            </div>

            <div className="recommendation-item priority-medium">
              <div className="recommendation-icon">
                <i className="icon-info"></i>
              </div>
              <div className="recommendation-text">
                <h4>Optimization Opportunity</h4>
                <p>Adjusting departure time of Train 12951 by 3 minutes could improve overall throughput by 8%.</p>
                <span className="recommendation-time">5 minutes ago</span>
              </div>
              <div className="recommendation-actions">
                <button className="btn btn-sm btn-primary">Apply</button>
                <button className="btn btn-sm btn-secondary">Dismiss</button>
              </div>
            </div>

            <div className="recommendation-item priority-low">
              <div className="recommendation-icon">
                <i className="icon-check"></i>
              </div>
              <div className="recommendation-text">
                <h4>Schedule Optimization Complete</h4>
                <p>All trains are operating within optimal parameters. No immediate action required.</p>
                <span className="recommendation-time">10 minutes ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="dashboard-section stats-section">
          <div className="section-header">
            <h2>Quick Stats</h2>
          </div>
          <div className="quick-stats">
            <div className="stat-item">
              <div className="stat-value">{Object.keys(simulationData.trains || {}).length}</div>
              <div className="stat-label">Active Trains</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {simulationData.metrics?.current?.average_delay?.toFixed(1) || '0.0'}min
              </div>
              <div className="stat-label">Avg Delay</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {((simulationData.metrics?.current?.capacity_utilization || 0) * 100).toFixed(0)}%
              </div>
              <div className="stat-label">Capacity</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {simulationData.metrics?.current?.throughput || 0}
              </div>
              <div className="stat-label">Completed</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
