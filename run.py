#!/usr/bin/env python3
"""
AI-Powered Train Traffic Control System
One-command startup script for SIH 2025 demonstration
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
from pathlib import Path

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚄 AI-Powered Train Traffic Control System            ║
    ║                                                              ║
    ║              Smart India Hackathon 2025                     ║
    ║                Ministry of Railways                          ║
    ║                   Team SHHHI                                 ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)
    print("🚀 Starting AI Traffic Control System...")
    print("=" * 60)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    
    # Check required Python packages
    required_packages = [
        'flask', 'torch', 'numpy', 'pandas', 'networkx', 
        'pulp', 'matplotlib', 'plotly', 'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing Python packages: {', '.join(missing_packages)}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies satisfied")
    return True

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = [
        'data', 'logs', 'static/css', 'static/js', 
        'models', 'simulation', 'presentation'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories ready")

def start_backend():
    """Start the Flask backend server"""
    print("🔧 Starting backend server...")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    # Start Flask app
    try:
        from backend.app import app, socketio, initialize_system
        
        # Initialize the system
        initialize_system()
        
        print("✅ Backend server starting on http://localhost:5000")
        
        # Start server in a separate thread
        def run_server():
            socketio.run(app, debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return False

def wait_for_server(url="http://localhost:5000", timeout=30):
    """Wait for server to be ready"""
    print("⏳ Waiting for server to be ready...")
    
    import requests
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except:
            pass
        time.sleep(1)
    
    print("❌ Server failed to start within timeout")
    return False

def open_browser():
    """Open browser with the application"""
    print("🌐 Opening browser...")
    
    urls = [
        "http://localhost:5000",  # Main dashboard
        "presentation/presentation.html"  # Presentation
    ]
    
    for url in urls:
        try:
            if url.startswith('http'):
                webbrowser.open(url)
            else:
                # Open local file
                file_path = os.path.abspath(url)
                webbrowser.open(f"file://{file_path}")
            time.sleep(1)
        except Exception as e:
            print(f"⚠️  Could not open {url}: {e}")

def show_instructions():
    """Show usage instructions"""
    instructions = """
    🎮 SYSTEM READY! Here's how to use it:
    
    📊 DASHBOARD (http://localhost:5000)
    ├── 🗺️  Network Map: View railway stations and tracks
    ├── 🚂 Train List: Monitor active trains and status
    ├── 📈 Metrics: Track performance indicators
    └── 🎛️  Controls: Manage simulation settings
    
    🎯 SIMULATION CONTROLS
    ├── ▶️  Start: Begin train simulation
    ├── ⏸️  Pause: Pause/resume simulation
    ├── ⏹️  Stop: Stop simulation
    ├── ⏭️  Step: Advance step by step
    └── 🔄 Speed: Adjust simulation speed
    
    🤖 AI OPTIMIZATION
    ├── 🧠 Manual: Trigger optimization on demand
    ├── 🔄 Auto: Continuous optimization mode
    ├── 💡 Recommendations: View AI suggestions
    └── 📊 Performance: Monitor effectiveness
    
    🎪 PRESENTATION
    ├── 📽️  Slides: 6-slide presentation for judges
    ├── ⌨️  Navigation: Arrow keys or buttons
    ├── 🎮 Live Demo: Launch dashboard from slides
    └── 📊 Live Metrics: Real-time performance data
    
    ⌨️  KEYBOARD SHORTCUTS
    ├── Space/→: Next slide
    ├── ←: Previous slide
    ├── Enter: Open live demo
    └── Esc: Exit fullscreen
    """
    print(instructions)

def main():
    """Main startup function"""
    try:
        print_banner()
        
        # Check dependencies
        if not check_dependencies():
            print("\n❌ Dependency check failed. Please install requirements first.")
            print("💡 Run: pip install -r requirements.txt")
            return 1
        
        # Setup directories
        setup_directories()
        
        # Start backend
        if not start_backend():
            print("\n❌ Failed to start backend server")
            return 1
        
        # Wait for server
        if not wait_for_server():
            print("\n❌ Server startup timeout")
            return 1
        
        # Open browser
        time.sleep(2)  # Give server a moment to fully initialize
        open_browser()
        
        # Show instructions
        show_instructions()
        
        print("\n🎉 AI Traffic Control System is running!")
        print("📱 Dashboard: http://localhost:5000")
        print("🎪 Presentation: presentation/presentation.html")
        print("\n⚠️  Press Ctrl+C to stop the server")
        
        # Keep the main thread alive
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Shutting down AI Traffic Control System...")
            print("👋 Thank you for using our SIH 2025 solution!")
            return 0
            
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
