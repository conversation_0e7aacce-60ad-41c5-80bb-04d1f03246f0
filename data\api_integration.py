"""
Real-time Data Integration Module
Connects to Indian Railways APIs and external data sources
"""

import requests
import json
import time
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import sqlite3
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TrainInfo:
    """Real-time train information"""
    train_number: str
    train_name: str
    current_station: str
    next_station: str
    delay: float
    status: str
    last_updated: datetime


@dataclass
class StationInfo:
    """Station information"""
    station_code: str
    station_name: str
    latitude: float
    longitude: float
    state: str
    zone: str


class IndianRailwayAPI:
    """Interface to Indian Railway APIs"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.base_url = "https://indianrailapi.com/api/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AI-TrafficControl/1.0',
            'Accept': 'application/json'
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds
        
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make rate-limited API request"""
        # Rate limiting
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        
        try:
            url = f"{self.base_url}/{endpoint}"
            if self.api_key:
                params = params or {}
                params['apikey'] = self.api_key
            
            response = self.session.get(url, params=params, timeout=10)
            self.last_request_time = time.time()
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"API request failed: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request error: {e}")
            return None
    
    def get_train_status(self, train_number: str, date: str = None) -> Optional[TrainInfo]:
        """Get real-time train status"""
        if not date:
            date = datetime.now().strftime("%d-%m-%Y")
        
        data = self._make_request(f"TrainStatus/apikey/{self.api_key}/TrainNumber/{train_number}/Date/{date}")
        
        if data and data.get('ResponseCode') == 200:
            train_data = data.get('TrainRoute', [])
            if train_data:
                current_station = None
                next_station = None
                delay = 0
                
                # Find current and next station
                for i, station in enumerate(train_data):
                    if station.get('HasArrived') and not station.get('HasDeparted'):
                        current_station = station.get('StationCode')
                        if i + 1 < len(train_data):
                            next_station = train_data[i + 1].get('StationCode')
                        delay = station.get('DelayArrival', 0)
                        break
                
                return TrainInfo(
                    train_number=train_number,
                    train_name=data.get('TrainName', ''),
                    current_station=current_station or '',
                    next_station=next_station or '',
                    delay=delay,
                    status='running' if current_station else 'scheduled',
                    last_updated=datetime.now()
                )
        
        return None
    
    def get_station_info(self, station_code: str) -> Optional[StationInfo]:
        """Get station information"""
        data = self._make_request(f"StationInfo/apikey/{self.api_key}/StationCode/{station_code}")
        
        if data and data.get('ResponseCode') == 200:
            station_data = data.get('Station', {})
            return StationInfo(
                station_code=station_code,
                station_name=station_data.get('StationName', ''),
                latitude=float(station_data.get('Latitude', 0)),
                longitude=float(station_data.get('Longitude', 0)),
                state=station_data.get('State', ''),
                zone=station_data.get('Zone', '')
            )
        
        return None
    
    def get_trains_between_stations(self, from_station: str, to_station: str) -> List[Dict]:
        """Get trains between two stations"""
        data = self._make_request(f"TrainBetweenStation/apikey/{self.api_key}/From/{from_station}/To/{to_station}")
        
        if data and data.get('ResponseCode') == 200:
            return data.get('Trains', [])
        
        return []


class DataCache:
    """Local data cache with SQLite backend"""
    
    def __init__(self, db_path: str = "data/cache.db"):
        self.db_path = db_path
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS train_status (
                    train_number TEXT,
                    timestamp DATETIME,
                    current_station TEXT,
                    next_station TEXT,
                    delay REAL,
                    status TEXT,
                    data TEXT,
                    PRIMARY KEY (train_number, timestamp)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS station_info (
                    station_code TEXT PRIMARY KEY,
                    station_name TEXT,
                    latitude REAL,
                    longitude REAL,
                    state TEXT,
                    zone TEXT,
                    last_updated DATETIME
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_logs (
                    timestamp DATETIME,
                    endpoint TEXT,
                    status_code INTEGER,
                    response_time REAL,
                    error_message TEXT
                )
            """)
    
    def cache_train_status(self, train_info: TrainInfo):
        """Cache train status data"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO train_status 
                (train_number, timestamp, current_station, next_station, delay, status, data)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                train_info.train_number,
                train_info.last_updated,
                train_info.current_station,
                train_info.next_station,
                train_info.delay,
                train_info.status,
                json.dumps(train_info.__dict__, default=str)
            ))
    
    def get_cached_train_status(self, train_number: str, max_age_minutes: int = 5) -> Optional[TrainInfo]:
        """Get cached train status if recent enough"""
        cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT data FROM train_status 
                WHERE train_number = ? AND timestamp > ?
                ORDER BY timestamp DESC LIMIT 1
            """, (train_number, cutoff_time))
            
            row = cursor.fetchone()
            if row:
                data = json.loads(row[0])
                data['last_updated'] = datetime.fromisoformat(data['last_updated'])
                return TrainInfo(**data)
        
        return None
    
    def cache_station_info(self, station_info: StationInfo):
        """Cache station information"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO station_info 
                (station_code, station_name, latitude, longitude, state, zone, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                station_info.station_code,
                station_info.station_name,
                station_info.latitude,
                station_info.longitude,
                station_info.state,
                station_info.zone,
                datetime.now()
            ))
    
    def get_cached_station_info(self, station_code: str) -> Optional[StationInfo]:
        """Get cached station information"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT station_code, station_name, latitude, longitude, state, zone
                FROM station_info WHERE station_code = ?
            """, (station_code,))
            
            row = cursor.fetchone()
            if row:
                return StationInfo(*row)
        
        return None


class RealTimeDataManager:
    """Manages real-time data collection and processing"""
    
    def __init__(self, api_key: str = None):
        self.api = IndianRailwayAPI(api_key)
        self.cache = DataCache()
        self.is_running = False
        self.update_thread = None
        self.train_numbers = []
        self.update_interval = 60  # seconds
        self.callbacks = []
    
    def add_train(self, train_number: str):
        """Add train to monitoring list"""
        if train_number not in self.train_numbers:
            self.train_numbers.append(train_number)
            logger.info(f"Added train {train_number} to monitoring")
    
    def remove_train(self, train_number: str):
        """Remove train from monitoring list"""
        if train_number in self.train_numbers:
            self.train_numbers.remove(train_number)
            logger.info(f"Removed train {train_number} from monitoring")
    
    def add_callback(self, callback):
        """Add callback for data updates"""
        self.callbacks.append(callback)
    
    def start_monitoring(self):
        """Start real-time data monitoring"""
        if self.is_running:
            return
        
        self.is_running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        logger.info("Started real-time data monitoring")
    
    def stop_monitoring(self):
        """Stop real-time data monitoring"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("Stopped real-time data monitoring")
    
    def _update_loop(self):
        """Main update loop"""
        while self.is_running:
            try:
                self._update_train_data()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in update loop: {e}")
                time.sleep(10)  # Wait before retrying
    
    def _update_train_data(self):
        """Update data for all monitored trains"""
        for train_number in self.train_numbers:
            try:
                # Check cache first
                cached_info = self.cache.get_cached_train_status(train_number)
                if cached_info:
                    self._notify_callbacks('train_update', cached_info)
                    continue
                
                # Fetch from API
                train_info = self.api.get_train_status(train_number)
                if train_info:
                    self.cache.cache_train_status(train_info)
                    self._notify_callbacks('train_update', train_info)
                    logger.info(f"Updated data for train {train_number}")
                else:
                    logger.warning(f"Failed to get data for train {train_number}")
                
                # Small delay between API calls
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error updating train {train_number}: {e}")
    
    def _notify_callbacks(self, event_type: str, data: Any):
        """Notify all registered callbacks"""
        for callback in self.callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"Error in callback: {e}")
    
    def get_train_status(self, train_number: str) -> Optional[TrainInfo]:
        """Get current train status (cached or fresh)"""
        # Try cache first
        cached_info = self.cache.get_cached_train_status(train_number, max_age_minutes=2)
        if cached_info:
            return cached_info
        
        # Fetch from API if not in cache
        train_info = self.api.get_train_status(train_number)
        if train_info:
            self.cache.cache_train_status(train_info)
        
        return train_info
    
    def get_station_info(self, station_code: str) -> Optional[StationInfo]:
        """Get station information (cached or fresh)"""
        # Try cache first
        cached_info = self.cache.get_cached_station_info(station_code)
        if cached_info:
            return cached_info
        
        # Fetch from API if not in cache
        station_info = self.api.get_station_info(station_code)
        if station_info:
            self.cache.cache_station_info(station_info)
        
        return station_info


# Mock data generator for demo purposes
class MockDataGenerator:
    """Generate mock real-time data for demonstration"""
    
    def __init__(self):
        self.trains = {
            "12951": {"name": "Mumbai Rajdhani", "route": ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"]},
            "12952": {"name": "New Delhi Rajdhani", "route": ["CSTM", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"]},
            "12615": {"name": "Grand Trunk Express", "route": ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"]},
        }
        self.current_positions = {train: 0 for train in self.trains}
        self.delays = {train: 0 for train in self.trains}
    
    def get_train_status(self, train_number: str) -> Optional[TrainInfo]:
        """Generate mock train status"""
        if train_number not in self.trains:
            return None
        
        train_data = self.trains[train_number]
        route = train_data["route"]
        position = self.current_positions[train_number]
        
        # Simulate movement
        if position < len(route) - 1:
            current_station = route[position]
            next_station = route[position + 1]
            
            # Randomly advance position
            if time.time() % 30 < 1:  # Move every ~30 seconds
                self.current_positions[train_number] = min(position + 1, len(route) - 1)
            
            # Simulate random delays
            if time.time() % 60 < 1:  # Update delay every ~60 seconds
                self.delays[train_number] += max(-2, min(5, (time.time() % 10) - 5))
                self.delays[train_number] = max(0, self.delays[train_number])
        else:
            current_station = route[-1]
            next_station = ""
        
        return TrainInfo(
            train_number=train_number,
            train_name=train_data["name"],
            current_station=current_station,
            next_station=next_station,
            delay=self.delays[train_number],
            status="running" if position < len(route) - 1 else "completed",
            last_updated=datetime.now()
        )


if __name__ == "__main__":
    # Test the data integration
    manager = RealTimeDataManager()
    
    # Add callback to print updates
    def print_update(event_type, data):
        print(f"Event: {event_type}, Data: {data}")
    
    manager.add_callback(print_update)
    
    # Add some trains to monitor
    manager.add_train("12951")
    manager.add_train("12952")
    
    # Start monitoring
    manager.start_monitoring()
    
    try:
        # Run for 30 seconds
        time.sleep(30)
    finally:
        manager.stop_monitoring()
